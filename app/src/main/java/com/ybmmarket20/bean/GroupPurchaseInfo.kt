package com.ybmmarket20.bean

import com.ybm.app.utils.JsonUtils

/**
 * <AUTHOR>
 * @desc    组合购
 * @date 2025/5/7
 * @updateDate 2025/5/9
 */
data class GroupPurchaseInfo(
    var combinedDiscountSub: Double? = 0.0, // 订单返回：总组合优惠
    var realPay: String? = "",  // 订单返回：总到手价
    var tips: String = "",// 订单返回：已选3种50盒
    var title: String = "",// 订单返回：组合购title：省钱套装·采购必选 加价购title：优惠精选
    var mainProduct: RowsBeanCombinedExt,  // 大搜返回：主品商品
    var subProducts: MutableList<RowsBeanCombinedExt>,  // 大搜返回：副品商品列表
    var combinedList:MutableList<RowsBeanCombinedExt>?=null,   // 订单返回:商品列表
    val carouselTime:Int = 4,   // 组合购轮播图间隔时间 s
    var errMsg:String? = null,
    var requestFlag:Boolean = false,    // 是否预加载过营销数据(忽略请求状态，只加载一次，再次请求只通过加购)
)

fun GroupPurchaseInfo.copyNew(index:Int) = GroupPurchaseInfo(
    combinedDiscountSub,realPay,tips?:"",title, JsonUtils.fromJson(JsonUtils.toJson(mainProduct),RowsBeanCombinedExt::class.java), mutableListOf(subProducts[index]),
    combinedList?.map { it.copy() } as? MutableList,carouselTime,errMsg
)

/**
 * 初始化组合购数据：首次
 */
fun GroupPurchaseInfo.initGroup(){
    for (subProduct in this.subProducts) {
        if (subProduct.actPt != null) {
            subProduct.qty = subProduct.actPt.skuStartNum
        } else if(subProduct.actPgby != null){
            subProduct.qty = subProduct.actPgby.skuStartNumToInt(0)
        }else {
            subProduct.qty = 0
        }
        subProduct.selectStatus = 1
    }
    this.mainProduct.selectStatus = 1
    if (this.mainProduct.actPt != null) {
        this.mainProduct.qty = this.mainProduct.actPt.skuStartNum
    } else if(this.mainProduct.actPgby != null){
        this.mainProduct.qty = this.mainProduct.actPgby.skuStartNumToInt(0)
    } else {
        this.mainProduct.qty = 0
    }
}

/**
 * 底部弹窗处理埋点数据
 */
fun GroupPurchaseInfo.handleTrackInfoForBottomPop(scmId: String?, qtListData: String?, searchRecPurchaseStrategyCode: String?) {
    val handleFun = {rowsBean: RowsBean ->
        rowsBean.mIsFromBottomPop = true
        rowsBean.isSingleCombinationPurchase = true
        rowsBean.scmId = scmId
        rowsBean.qtListData = qtListData
        rowsBean.searchRecPurchaseStrategyCode = searchRecPurchaseStrategyCode
    }
    handleFun(mainProduct)
    subProducts.forEach(handleFun)
    combinedList?.forEach(handleFun)
}

fun GroupPurchaseInfo.copyList(): GroupPurchaseInfoCopy{
    // 转为List 供 banner使用。 主品不复用
    val itemGroupInfoSub = GroupPurchaseInfoCopy(mutableListOf<GroupPurchaseInfo>())
    this.subProducts.forEachIndexed { index,it->
        itemGroupInfoSub.groupPurchaseInfos.add(copyNew(index))
    }
    return itemGroupInfoSub
}

/**
 * 初始化加价购数据：首次
 */
fun GroupPurchaseInfo.initAdditonal(){
    for (subProduct in this.subProducts) {
        if (subProduct.actPt == null) {
            subProduct.qty = 0
        } else {
            subProduct.qty = subProduct.actPt.skuStartNum
        }
    }
}