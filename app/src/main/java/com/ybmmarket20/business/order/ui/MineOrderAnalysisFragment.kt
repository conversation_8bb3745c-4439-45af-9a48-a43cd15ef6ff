package com.ybmmarket20.business.order.ui

import com.ybmmarket20.bean.CouponsTipsResponse
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.home.MainActivity
import com.ybmmarket20.xyyreport.page.orderList.OrderListReport
import com.ybmmarket20.xyyreport.page.orderList.OrderListReportUtil
import com.ybmmarket20.xyyreport.spm.SpmUtil

abstract class MineOrderAnalysisFragment : BaseFragment() {

    private var isFirstReportPv = false
    var mTargetTabIndex = -1
    var couponId = ""


    /**
     * pv
     */
    fun pvTrack() {
        if (mTargetTabIndex != getVpCurrIndex()) return
        mTargetTabIndex = -1
        pvTrackInternal()
    }

    private fun pvTrackInternal() {
        try {
            val orderStatus = getOrderListAnalysisParams()?.getOrderStatus() ?: 0
            val orderStatusText =
                OrderListReportUtil.getOrderStatusInfoByStatus(orderStatus)?.orderReportTag
            OrderListReport.pvOrderList(requireActivity(), orderStatusText)
            SpmUtil.checkAnalysisContext(requireActivity()) {
                (notNullActivity as MainActivity).trackCommonTabComponentExposure(it)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun pvTrackFirst() {
        if (!isFirstReportPv) {
            isFirstReportPv = true
            mTargetTabIndex = -1
            pvTrackInternal()
        }
    }

    fun onTrackOrderBannerExposure(cmsGroupId: Int) {
        SpmUtil.checkAnalysisContext(requireActivity()) {
            OrderListReport.trackOrderBannerExposure(
                it,
                cmsGroupId,
                getBannerExposureBtnStr(cmsGroupId),
                if(cmsGroupId==1) couponId else ""
            )
        }
    }

    fun onTrackOrderBannerClick(cmsGroupId: Int) {
        SpmUtil.checkAnalysisContext(requireActivity()) {
            OrderListReport.trackOrderBannerClick(
                it,
                cmsGroupId,
                getBannerClickBtnStr(cmsGroupId),
                if(cmsGroupId==1) couponId else ""
            )
        }
    }

    private fun getBannerClickBtnStr(type: Int) = when (type) {
        1 -> "优惠券"
        2 -> "购物金立即充值"
        3 -> "消费返点击"
        4 -> "广告图片立即体验"
        5 -> "更新资质"
        else->""
    }


    private fun getBannerExposureBtnStr(type: Int) = when (type) {
        1 -> "优惠券"
        2 -> "购物金立即充值"
        3 -> "消费返"
        4 -> "广告图片立即体验"
        5 -> "更新资质"
        else->""
    }


    fun setCouponId(couponDate: CouponsTipsResponse) {
        couponDate.scenes?.forEachIndexed { index, scene ->
            if (index == 0) {
                scene.coupons?.forEachIndexed { idx, coupon ->
                    if (idx == 0) {
                        couponId = coupon.couponId ?: ""
                    }
                }
            }
        }
    }

    open fun getOrderListAnalysisParams(): IOrderListAnalysisParams? = null

    abstract fun getVpCurrIndex(): Int
}