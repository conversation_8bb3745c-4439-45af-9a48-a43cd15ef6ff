package com.ybmmarket20.business.shop.ui

import android.annotation.SuppressLint
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.View.VISIBLE
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.ybm.app.bean.NetError
import com.ybmmarket20.R
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.SearchAggsBean
import com.ybmmarket20.bean.SearchResultBean
import com.ybmmarket20.bean.ShopHomeIndexBean.Floor
import com.ybmmarket20.business.shop.ShopGoodsTabAnalysisFragment
import com.ybmmarket20.common.BaseFragment
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.LazyFragment
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.report.coupon.CouponEntryType
import com.ybmmarket20.report.coupon.ICouponEntryType
import com.ybmmarket20.utils.AdapterUtils
import com.ybmmarket20.utils.SpUtil
import com.ybmmarket20.utils.analysis.FlowData
import com.ybmmarket20.utils.analysis.XyyIoUtil
import com.ybmmarketkotlin.adapter.GoodListAdapterNew
import com.ybmmarketkotlin.adapter.GoodsListAdapterNewCategory
import kotlinx.android.synthetic.main.fragment_shop_goods_tab.cbCanUseCoupon
import kotlinx.android.synthetic.main.fragment_shop_goods_tab.llFilter
import kotlinx.android.synthetic.main.fragment_shop_goods_tab.rg_property
import kotlinx.android.synthetic.main.fragment_shop_goods_tab.rv_floor_index
import kotlinx.android.synthetic.main.fragment_shop_goods_tab.rv_goodslist
import kotlinx.android.synthetic.main.fragment_shop_home.smartrefresh

class ShopGoodsTabFragment : ShopGoodsTabAnalysisFragment(), ICouponEntryType {

    var orgId: String? = null
    var shopCode: String? = null
    var floorId: String? = null
    private var floorIndex: Int = 0
    var floorName:String? = null
        set(value) {
            field = value
        }
//    var floorType: String? = null

    private var property: String = "smsr.sale_num"
    private var sortNavIndex = 1
    private var sortNavText = "综合"

    var floorData: MutableList<Floor> = mutableListOf()
    var floorAdapter: FloorAdapter? = null
    var goodsData: MutableList<RowsBean> = mutableListOf()
    private var goodsListAdapter: GoodsListAdapterNewCategory? = null
    var isFloorSelectAll: Boolean = false
    private var mAnchorCsuId: String? = null
    private var source: String? = ""
    private var isFilterUnableAddCart: String? = ""
    //是否展示筛选项
    private var mIsShowFilter = true
    override fun getGoodsListAdapter(): GoodsListAdapterNewCategory? = goodsListAdapter

    @SuppressLint("NotifyDataSetChanged")
    override fun initData(content: String?) {

        orgId = arguments?.getString("orgId")
        shopCode = arguments?.getString("shopCode")
        mAnchorCsuId = arguments?.getString("anchorCsuId")
        source = arguments?.getString("source")
        isFilterUnableAddCart = arguments?.getString("isFilterUnableAddCart")
        floorAdapter = FloorAdapter(R.layout.item_shop_floor, floorData)
        rv_floor_index?.adapter = floorAdapter
        rv_floor_index?.layoutManager = LinearLayoutManager(context)
        floorAdapter?.setOnItemClickListener { _, _, position ->
            if (!floorData[position].isSelect) {
                mIsShowFilter = floorData[position].isShowFilter
                if (floorData[position].floorName.equals("全部")) {
                    cbCanUseCoupon.visibility = VISIBLE
                    isFloorSelectAll = true
                } else {
                    cbCanUseCoupon.visibility = View.GONE
                    isFloorSelectAll = false
                }
                floorData.forEach { it.isSelect = false }

                floorData[position].isSelect = true
                floorId = floorData[position].floorId
                floorIndex = position
                floorName = floorData[position].floorName
                //floorType = floorData.get(position).floorType
                floorAdapter?.notifyDataSetChanged()
                // 获取对应楼层的商品信息
                getNewGoodsList()
            }
        }

        cbCanUseCoupon.setOnCheckedChangeListener { _, isCheck ->
            isFloorSelectAll = isCheck
            showProgress()
            getNewGoodsList()
        }

        goodsListAdapter = GoodsListAdapterNewCategory(this, goodsData,isAddCartShowPopupWindow = isFromShopCart()).apply {
            setEmptyView(notNullActivity, R.layout.layout_empty_view_all_goods, R.drawable.icon_empty, "哇哦，没有找到相关商品")
            mIsSupportQtExposure = true
            isShowShopInfo = false
            showUnderlinePrice = false
            mIsFromShopCartGatherOrders = isFromShopCart()
        }

        rv_goodslist.itemAnimator = null
        rv_goodslist.adapter = goodsListAdapter
        rv_goodslist.layoutManager = LinearLayoutManager(context)
        goodsListAdapter?.setOnLoadMoreListener({ getLoadMoreGoodsList() }, rv_goodslist)
        smartrefresh?.setOnRefreshListener {
            getNewGoodsList()
        }
        rg_property?.setOnCheckedChangeListener { _, checkedId ->
            when (checkedId) {
                R.id.rb_01 -> {
                    property = "smsr.sale_num"
                    XyyIoUtil.track("shopHome_Sort_Click", hashMapOf("text" to "综合"))
                    sortNavIndex = 1
                    sortNavText = "综合"
                }    // 综合排序
                R.id.rb_02 -> {
                    property = "spa.sale_num"
                    XyyIoUtil.track("shopHome_Sort_Click", hashMapOf("text" to "销量"))
                    sortNavIndex = 2
                    sortNavText = "销量"
                }     // 销量排序
            }
            getNewGoodsList()
        }
    }

    private fun isFromShopCart() = source == "1"

    private fun getGoodsIndex() {
        val aggparams = RequestParams().apply {
            shopCode?.let { put("shopCodes", shopCode) }
            orgId?.let { put("orgId", orgId) }
            put("isShopPage", "1")
        }

        HttpManager.getInstance().post(AppNetConfig.SORTNET_aggs, aggparams, object : BaseResponse<SearchAggsBean?>() {
            @SuppressLint("NotifyDataSetChanged")
            override fun onSuccess(content: String?, obj: BaseBean<SearchAggsBean?>?, t: SearchAggsBean?) {
                t?.aggregations?.catStats?.let {
                    floorData.clear()
                    floorData.add(Floor().apply {
                        floorName = "全部"
                    })
                    cbCanUseCoupon?.post {
                        cbCanUseCoupon?.visibility = VISIBLE
                    }
                    isFloorSelectAll = true
                    it.forEach {
                        floorData.add(Floor().apply {
                            floorId = it.key
                            floorName = it.showName
                            isShowFilter = it.showFilter == 1
                        })
                    }
                    if (floorData.size > 0) {
                        floorData[0].isSelect = true
                        floorId = floorData[0].floorId
                        floorName = floorData[0].floorName
                        //floorType = floorData.get(0).floorType
                        getNewGoodsList()
                    }

                    floorAdapter?.notifyDataSetChanged()
                }
            }
        })
    }


    /**
     * 请求数据
     */
    private fun getLoadMoreGoodsList() {
        HttpManager.getInstance().post(AppNetConfig.SORTNET,
            getNewGoodListRequestParams(true), object : BaseResponse<SearchResultBean?>() {
                override fun onSuccess(content: String?, obj: BaseBean<SearchResultBean?>?, brandBean: SearchResultBean?) {
                    brandBean?.let {
                        addGoodsReportParams(brandBean, floorIndex, floorName, sortNavIndex, sortNavText, mIsShowFilter)
                        updateGoodsData(false, it)
                    }
                }
            })
    }

    /**
     */
    private fun getNewGoodsList() {
        showProgress()
        val newGoodParams = getNewGoodListRequestParams(false)
        HttpManager.getInstance()
            .post(AppNetConfig.SORTNET, newGoodParams, object : BaseResponse<SearchResultBean?>() {
                override fun onSuccess(content: String?, obj: BaseBean<SearchResultBean?>, brandBean: SearchResultBean?) {
                    dismissProgress()
                    smartrefresh?.finishRefresh()
                    goodsData.clear()
                    brandBean?.let {
                        addGoodsReportParams(brandBean, floorIndex, floorName, sortNavIndex, sortNavText, mIsShowFilter)
                        updateGoodsData(true, it)
                        try {
                            if (llFilter == null) {
                                Handler(Looper.getMainLooper()).post {
                                    llFilter.isVisible = mIsShowFilter
                                }
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                        }
                    }
                }

                override fun onFailure(error: NetError) {
                    dismissProgress()
                    smartrefresh.finishRefresh()
                }
            })
    }

    private var searchMoreParams: RequestParams? = null

    /**
     *  更新商品信息
     */
    private fun updateGoodsData(isRefresh: Boolean, rowsBeans: SearchResultBean) {
        goodsListAdapter?.flowData = FlowData(rowsBeans.sptype, rowsBeans.spid, rowsBeans.sid, "", "", null)
        searchMoreParams = rowsBeans.requestParams
        rowsBeans.rows?.let {
            AdapterUtils.addLocalTimeForRows(rowsBeans.rows)
            goodsListAdapter?.let {
                AdapterUtils.notifyAndControlLoadmoreStatus(rowsBeans.rows, it, isRefresh, rowsBeans.isEnd)
            }
            // 请求并更新折后价
            goodsListAdapter?.let { AdapterUtils.getAfterDiscountPrice(rowsBeans.rows, it) }
        }
    }

    /**
     * 请求参数
     */
    private fun getNewGoodListRequestParams(loadMore: Boolean): RequestParams? =
        if (loadMore) searchMoreParams else RequestParams().apply {
            if (mIsShowFilter) {
                put("property", property)
                if (property.endsWith("spa.sale_num")){
                    put("direction", "desc")
                }
                if (isFloorSelectAll && cbCanUseCoupon.isChecked) {
                    put("isAvailableCoupons", "1")
                }
            }
            put("merchantId", SpUtil.getMerchantid())
            //isThirdCompany 字段废弃，后端使用orgId和shopCodes判断店铺类型
            orgId?.let {
                put("orgId", orgId)
            }
            shopCode?.let {
                put("shopCodes", shopCode)
            }
            floorId?.let { put("categoryFirstId", floorId) }
//            put("sptype", mFlowData.spType)
            put("spFrom", "2")
            put("sptype", "14")
            put("isFilterUnableAddCart", isFilterUnableAddCart)
            //全部选项并且锚点商品的id不为空
            if (!mAnchorCsuId.isNullOrEmpty() && floorId.isNullOrEmpty()) {
                put("anchorCsuId", mAnchorCsuId)
            }
        }


    override fun initTitle() {
    }

    override fun getParams(): RequestParams? = null

    override fun getUrl(): String? = null

    override fun getLayoutId(): Int = R.layout.fragment_shop_goods_tab
    override fun loadData() {
        getGoodsIndex()
        XyyIoUtil.track("shopHome_Sort_Click", hashMapOf("text" to "综合"))
    }

    override fun getCouponEntryType(): String = CouponEntryType.COUPON_ENTRY_TYPE_SHOP_GOODS
}