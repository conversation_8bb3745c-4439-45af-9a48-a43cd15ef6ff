package com.ybmmarket20.common;

import android.app.Activity;
import android.content.Context;

import androidx.fragment.app.Fragment;

import android.os.Handler;
import android.util.Log;
import android.view.View;

import com.xyyio.analysis.stat.XyyioParams;
import com.ybmmarket20.utils.analysis.XyyIoUtil;

import java.util.List;


/**
 * Created by asus on 2016/3/10.
 */
public abstract class YBMBaseFragment extends Fragment {

    private BaseActivity mActivity;

    private boolean isShowing = false;

    private final Handler handler = new Handler();

    @Override
    public void onAttach(Context context) {
        super.onAttach(context);
        if (context instanceof Activity) {
            mActivity = (BaseActivity) context;
        }
    }

    public Activity getNotNullActivity() {
        return mActivity;
    }

    public void showProgress() {
        if (mActivity != null && !mActivity.isFinishing() && !mActivity.isDestroyed()) {
            mActivity.showProgress();
        }
    }

    public boolean isShowingProgress() {
        if (mActivity != null && !mActivity.isFinishing() && !mActivity.isDestroyed()) {
            return mActivity.isShowingProgress();
        }
        return false;
    }

    public void showProgress(String str) {
        if (mActivity != null && !mActivity.isFinishing() && !mActivity.isDestroyed()) {
            mActivity.showProgress(str);
        }
    }

    public void dismissProgress() {
        if (mActivity != null && !mActivity.isFinishing() && !mActivity.isDestroyed()) {
            mActivity.dismissProgress();
        }
    }


    // 隐藏软键盘
    public void hideSoftInput() {
        if (mActivity != null && !mActivity.isFinishing() && !mActivity.isDestroyed()) {
            mActivity.hideSoftInput();
        }
    }

    public void hideSoftInput(View view) {
        if (mActivity != null && !mActivity.isFinishing() && !mActivity.isDestroyed()) {
            mActivity.hideSoftInput(view);
        }
    }


    public void onVisibleChanged(boolean isVisible) {
        try {

            isVisible = isVisibleOnScreen(isVisible);
            String pageName = getClass().getSimpleName();
            Log.d("guan", pageName + ",onVisibleChanged:" + isVisible);
            if (isShowing != isVisible) {
                isShowing = isVisible;
                Log.d("guan", pageName + ",onVisibleChanged change:" + isVisible);
                if (isVisible) {
                    // 展示
                    XyyIoUtil.track("PV-" + pageName, null, YBMBaseFragment.this);
                } else {
                    // 隐藏
                    XyyIoUtil.track("PD-" + pageName, null, YBMBaseFragment.this);
                }
            }
        } catch (Exception ignore) {
        }
    }


    @Override
    public void onResume() {
        super.onResume();
        handler.post(new Runnable() {
            @Override
            public void run() {
                String pageName = YBMBaseFragment.this.getClass().getSimpleName();
                Log.d("guan", pageName + ",onResume,isVisible:" + isVisible() + ",isAdd:" + isAdded() + ",isHidden:" + isHidden());
                onVisibleChanged(isVisible());
            }
        });
    }


    @Override
    public void onPause() {
        super.onPause();
        handler.post(new Runnable() {
            @Override
            public void run() {
                String pageName = YBMBaseFragment.this.getClass().getSimpleName();

                Log.d("guan", pageName + ",onPause,isVisible:" + isVisible());
                onVisibleChanged(false);
            }
        });

    }

    @Override
    public void onHiddenChanged(boolean hidden) {
        super.onHiddenChanged(hidden);

        handler.post(new Runnable() {
            @Override
            public void run() {

                String pageName = YBMBaseFragment.this.getClass().getSimpleName();
                Log.d("guan", pageName + ",onHiddenChanged:" + hidden + ",isVisible:" + isVisible());
                onVisibleChanged(!hidden);

                try {

                    if (isAdded()) {
                        List<Fragment> fragments = getChildFragmentManager().getFragments();
                        for (Fragment fragment : fragments) {
                            if (fragment instanceof YBMBaseFragment) {
                                ((YBMBaseFragment) fragment).onVisibleChanged(!hidden);
                            }
                        }
                    }
                } catch (Exception ignore) {
                }
            }
        });

    }

    private boolean isVisibleOnScreen(boolean isCanShowing) {
        if (isCanShowing && getUserVisibleHint() && isVisible()) {
            if (getParentFragment() == null) {
                return true;
            }
            if (getParentFragment() instanceof YBMBaseFragment) {
                return ((YBMBaseFragment) getParentFragment()).isVisibleOnScreen(isCanShowing);
            } else {
                return getParentFragment().isVisible();
            }
        }
        return false;
    }

    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        handler.post(new Runnable() {
            @Override
            public void run() {
                String pageName = YBMBaseFragment.this.getClass().getSimpleName();
                Log.d("guan", pageName + ",setUserVisibleHint:" + isVisibleToUser + ",isVisible:" + isVisible());
                onVisibleChanged(isVisibleToUser);
                try {
                    if (isAdded()) {
                        List<Fragment> fragments = getChildFragmentManager().getFragments();
                        for (Fragment fragment : fragments) {
                            if (fragment instanceof YBMBaseFragment) {
                                ((YBMBaseFragment) fragment).onVisibleChanged(isVisibleToUser);

                            }
                        }
                    }
                } catch (Exception ignore) {
                }
            }
        });

    }

}