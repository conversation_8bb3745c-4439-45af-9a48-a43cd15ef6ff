package com.ybmmarket20.utils

import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import com.ybm.app.adapter.YBMBaseAdapter
import com.ybm.app.bean.NetError
import com.ybm.app.utils.JsonUtils
import com.ybmmarket20.activity.PaymentActivity
import com.ybmmarket20.bean.BaseBean
import com.ybmmarket20.bean.CartDataBean
import com.ybmmarket20.bean.GroupPurchaseInfo
import com.ybmmarket20.bean.HomeFeedRows
import com.ybmmarket20.bean.RowsBean
import com.ybmmarket20.bean.RowsBeanCombinedExt
import com.ybmmarket20.bean.RowsPriceDiscount
import com.ybmmarket20.bean.SearchRowsBean
import com.ybmmarket20.bean.SettleBean
import com.ybmmarket20.bean.ShopInfoSxpList
import com.ybmmarket20.bean.WrapRecommendBean
import com.ybmmarket20.bean.cart.CartItemBean
import com.ybmmarket20.bean.isStep
import com.ybmmarket20.bean.setOrderData
import com.ybmmarket20.common.AlertDialogEx
import com.ybmmarket20.common.BaseActivity
import com.ybmmarket20.common.BaseResponse
import com.ybmmarket20.common.RequestParams
import com.ybmmarket20.common.YBMAppLike
import com.ybmmarket20.common.util.ToastUtils
import com.ybmmarket20.constant.AppNetConfig
import com.ybmmarket20.constant.IntentCanst
import com.ybmmarket20.home.newpage.adapter.HomeFeedStreamAdapter
import com.ybmmarket20.network.HttpManager
import com.ybmmarket20.utils.RoutersUtils.dismissProgress

/**
 * <AUTHOR> Brin
 * @date : 2020/10/27 - 11:47
 * @Description :
 * @version
 */
object AdapterUtils {

    // 批量获取购物车商品的折后价
    fun getAfterDiscountPriceForCart(newsAdd: List<CartItemBean>, goodListAll: List<CartItemBean>, goodsAdapter: YBMBaseAdapter<*>) {
        if (newsAdd.isNullOrEmpty() || goodListAll.isNullOrEmpty()) {
            return
        }
        val rowsIds = StringBuffer()
        for (item in newsAdd) {
            if (CartItemBean.content_recommend == item.itemType && !item?.rowsBean?.rangePriceBean.isStep()) {
                // 提取购物车的为你推荐商品id出来
                item?.rowsBean?.id?.takeIf { it != 0L }?.let { rowsIds.append(it).append(",") }
            }/*else{
                // 提取购物车的商品id出来
                item?.id?.takeIf { it != 0 }?.let { rowsIds.append(it).append(",") }
            }*/
        }
        if (rowsIds.isNullOrEmpty()) {
            return
        }
        rowsIds.deleteCharAt(rowsIds.length - 1)
        //LogUtils.tag("price_after_discount").e(rowsIds)
        val requestParams = RequestParams()
        requestParams.put("skuIds", rowsIds.toString())
        requestParams.url = AppNetConfig.LIST_PRODUCT_DISCOUNT
        HttpManager.getInstance().post(requestParams, object : BaseResponse<List<RowsPriceDiscount?>?>() {
            override fun onSuccess(content: String?, obj: BaseBean<List<RowsPriceDiscount?>?>?, rowsPriceDiscounts: List<RowsPriceDiscount?>?) {
                if (obj != null && !rowsPriceDiscounts.isNullOrEmpty()) {
                    newsAdd.forEach {
                        if (CartItemBean.content_recommend == it.itemType) {
                            val rowsId = it?.rowsBean?.id
                            var price: String?
                            rowsPriceDiscounts.filter { it?.skuId == rowsId }.let {
                                price = it.takeIf { it.size > 0 }?.get(0)?.price
                            }
                            goodListAll.forEach { it ->
                                it.takeIf { rowsId == it?.rowsBean?.id }?.let { it?.rowsBean?.showPriceAfterDiscount = price }
                            }
                        }
                        /*else{
                            val rowsId = it?.id?.toLong()
                            var price: String?
                            rowsPriceDiscounts.filter { it?.skuId == rowsId }.let {
                                price = it.takeIf { it.size > 0 }?.get(0)?.price
                            }
                            goodListAll.forEach { it ->
                                it.takeIf { rowsId == it?.id?.toLong() }?.let { it?.preferentialPrice = price }
                            }
                        }*/

                    }
                    goodsAdapter.notifyDataSetChanged()
                }
            }
        })
    }


    /**
     * 批量获取商品的折后价
     */
    fun <T : RowsBean> getAfterDiscountPrice(newsAdd: MutableList<T>?, goodsAdapter: YBMBaseAdapter<*>) {
        getAfterDiscountPrice(newsAdd, goodsAdapter, false)
    }

    /**
     * 批量获取商品的折后价
     * 注：
     *  isShowStepPriceAfterPrice: 阶梯价是否展示折后价
     *  新需求改动：拼团品，包邮品都有
     */
    fun <T : RowsBean> getAfterDiscountPrice(newsAdd: MutableList<T>?, goodsAdapter: YBMBaseAdapter<*>, isShowStepPriceAfterPrice: Boolean) {
        if (newsAdd.isNullOrEmpty() || goodsAdapter.data.isNullOrEmpty()) {
            return
        }
        // 如果资质未通过审核则不需要请求折后价
        if (!AuditStatusSyncUtil.getInstance().isAuditFirstPassed) {
            return
        }
        val rowsIds = newsAdd.asSequence()
//            .filter { (it.actPt == null || it.actPt.assembleStatus != 1) && it.actSk == null && (isShowStepPriceAfterPrice || !it.rangePriceBean.isStep()) }
            .map { it.id }
            .joinToString(",")
        val requestParams = RequestParams()
        requestParams.put("skuIds", rowsIds)
        requestParams.url = AppNetConfig.LIST_PRODUCT_DISCOUNT
        HttpManager.getInstance().post(requestParams, object : BaseResponse<List<RowsPriceDiscount?>?>() {
            override fun onSuccess(content: String?, obj: BaseBean<List<RowsPriceDiscount?>?>?, rowsPriceDiscounts: List<RowsPriceDiscount?>?) {
                if (obj != null && !rowsPriceDiscounts.isNullOrEmpty()) {
                    val priceAfterDiscountMap = rowsPriceDiscounts.associateBy { it?.skuId }
                    goodsAdapter.data.forEach { row ->
                        row?.apply {
                            if (this is RowsBean && priceAfterDiscountMap.containsKey(id)) {
                                val discountPrice = priceAfterDiscountMap[id]?.inHandPrice?: 0.0
                                try {
                                    //阶梯价 折后价小于阶梯价最小值才显示
                                    if (actPt != null && actPt.isStepPrice()){ //拼团品阶梯价
                                        if ((discountPrice < (actPt.minSkuPrice?.toDouble()?:0.0))){
                                            showPriceAfterDiscount = priceAfterDiscountMap[id]?.price
                                        }
                                    }else if (rangePriceBean.isStep()){ //普通品阶梯价
                                        if ((discountPrice < (rangePriceBean.minSkuPrice?.toDouble()?:0.0))){
                                            showPriceAfterDiscount = priceAfterDiscountMap[id]?.price
                                        }
                                    }else if (discountPrice <= showPrice) {
                                        showPriceAfterDiscount = priceAfterDiscountMap[id]?.price
                                    }
                                }catch (e:Exception){
                                    e.printStackTrace()
                                }
                            } else if (this is SearchRowsBean && cardType != 2 && priceAfterDiscountMap.containsKey(productInfo?.id?: -1)) {
                                val discountPrice = priceAfterDiscountMap[productInfo?.id?: -1]?.inHandPrice?: 0.0
                                try {
                                    //阶梯价 折后价小于阶梯价最小值才显示
                                    productInfo?.let {
                                        if (it.limitFullDiscountActInfo != null){ //限时加补 不显示折后价
//                                            if (discountPrice < it.limitFullDiscountActInfo.limitFullDiscount){
//                                                it.showPriceAfterDiscount = priceAfterDiscountMap[it.id]?.price
//                                            }
                                        } else if (it.actPt != null && it.actPt.isStepPrice()){ //拼团品阶梯价
                                            if (discountPrice < (it.actPt.minSkuPrice?.toDouble()?:0.0)){
                                                it.showPriceAfterDiscount = priceAfterDiscountMap[it.id]?.price
                                            }
                                        }else if (it.rangePriceBean.isStep()){ //普通品阶梯价
                                            if ((discountPrice < (it.rangePriceBean.minSkuPrice?.toDouble()?:0.0))){
                                                it.showPriceAfterDiscount = priceAfterDiscountMap[it.id]?.price
                                            }
                                        }else if (discountPrice <= it.showPrice) {
                                            it.showPriceAfterDiscount = priceAfterDiscountMap[it.id]?.price
                                        }
                                    }
                                }catch (e:Exception){
                                    e.printStackTrace()
                                }
                            } else if (this is HomeFeedRows && type == 1 && priceAfterDiscountMap.containsKey(productInfo?.id)) {
                                //首页商品
                                val discountPrice = priceAfterDiscountMap[productInfo?.id]?.inHandPrice?: 0.0
                                if (discountPrice <= (productInfo?.fob ?: 0.0)) {
                                    productInfo?.showPriceAfterDiscount = priceAfterDiscountMap[productInfo?.id]?.price
                                }
                            } else if (this is HomeFeedRows && type == 2) {
                                //首页店铺
                                shopInfo?.shopGoods?.forEach {
                                    if (priceAfterDiscountMap.containsKey(it.id)) {
                                        val discountPrice = priceAfterDiscountMap[it?.id]?.inHandPrice?: 0.0
                                        if (discountPrice <= (it.fob)) {
                                            it.showPriceAfterDiscount = priceAfterDiscountMap[it.id]?.price
                                        }
                                    }
                                }
                            }
                        }
                    }
                    Handler(Looper.getMainLooper()).post {
                        goodsAdapter.notifyDataSetChanged()
                    }
                }
            }
        })
    }


    //批量获取首页Feed流商品的折扣价
    fun <T : RowsBean> getHomeFeedAfterDiscountPrice(newsAdd: MutableList<T>?, goodsAdapter: HomeFeedStreamAdapter) {
        if (newsAdd.isNullOrEmpty() || goodsAdapter.mDataList.isEmpty()) {
            return
        }
        // 如果资质未通过审核则不需要请求折后价
        if (!AuditStatusSyncUtil.getInstance().isAuditFirstPassed) {
            return
        }
        val rowsIds = newsAdd.asSequence()
//            .filter { (it.actPt == null || it.actPt.assembleStatus != 1) && it.actSk == null && (isShowStepPriceAfterPrice || !it.rangePriceBean.isStep()) }
                .map { it.id }
                .joinToString(",")
        val requestParams = RequestParams()
        requestParams.put("skuIds", rowsIds)
        requestParams.url = AppNetConfig.LIST_PRODUCT_DISCOUNT
        HttpManager.getInstance().post(requestParams, object : BaseResponse<List<RowsPriceDiscount?>?>() {
            override fun onSuccess(content: String?, obj: BaseBean<List<RowsPriceDiscount?>?>?, rowsPriceDiscounts: List<RowsPriceDiscount?>?) {
                if (obj != null && !rowsPriceDiscounts.isNullOrEmpty()) {
                    val priceAfterDiscountMap = rowsPriceDiscounts.associateBy { it?.skuId }
                    goodsAdapter.mDataList.filter { it.feedType == "3" }.mapNotNull { it.product}.forEach { row ->
                        row?.apply {
                            if (this is RowsBean && priceAfterDiscountMap.containsKey(id)) {
                                val discountPrice = priceAfterDiscountMap[id]?.inHandPrice?: 0.0
                                try {
                                    if (actPt != null && actPt.isStepPrice() && (discountPrice < (actPt.minSkuPrice?.toDouble()?:0.0))){ //拼团品阶梯价
                                        showPriceAfterDiscount = priceAfterDiscountMap[id]?.price
                                    }else if (rangePriceBean.isStep() && (discountPrice < (rangePriceBean.minSkuPrice?.toDouble()?:0.0))){ //平销品阶梯价 折后价小于阶梯价最小值才显示
                                        showPriceAfterDiscount = priceAfterDiscountMap[id]?.price
                                    }else if (discountPrice <= showPrice) {
                                        showPriceAfterDiscount = priceAfterDiscountMap[id]?.price
                                    }
                                }catch (e:Exception){
                                    e.printStackTrace()
                                }
                            }
                        }
                    }
                    Handler(Looper.getMainLooper()).post {
                        goodsAdapter.notifyDataSetChanged()
                    }
                }
            }
        })
    }


    /**
     * 批量获取商品的折后价(带店铺)
     * 注：
     *  1. 拼团中的商品不获取折后价
     */
    fun getAfterDiscountPriceWithShop(newsAdd: List<HomeFeedRows>?, goodsAdapter: YBMBaseAdapter<*>) {
        val rowsBeans = mutableListOf<RowsBean>()
        newsAdd?.forEach { row ->
            if (row.type == 1) {
                row.productInfo?.let(rowsBeans::add)
            } else if (row.type == 2) {
                row.shopInfo?.shopGoods?.let(rowsBeans::addAll)
            }
        }
        getAfterDiscountPrice(rowsBeans, goodsAdapter)
    }


    // 批量获取商品的折后价
    @Deprecated("just for old home recommend, should be del later")
    fun getAfterDiscountPriceForHomeRecommend(
        newsAdd: List<WrapRecommendBean>,
        goodListAll: List<WrapRecommendBean>,
        goodsAdapter: YBMBaseAdapter<*>
    ) {
        if (newsAdd.isNullOrEmpty() || goodListAll.isNullOrEmpty()) {
            return
        }
        val rowsIds = StringBuffer()
        for (item in newsAdd) {
            if (!item.rowsBean.rangePriceBean.isStep()){
                rowsIds.append(item.rowsBean.id).append(",")
            }
        }
        rowsIds.deleteCharAt(rowsIds.length - 1)
        //LogUtils.tag("price_after_discount").e(rowsIds)
        val requestParams = RequestParams()
        requestParams.put("skuIds", rowsIds.toString())
        requestParams.url = AppNetConfig.LIST_PRODUCT_DISCOUNT
        HttpManager.getInstance().post(requestParams, object : BaseResponse<List<RowsPriceDiscount?>?>() {
            override fun onSuccess(content: String?, obj: BaseBean<List<RowsPriceDiscount?>?>?, rowsPriceDiscounts: List<RowsPriceDiscount?>?) {
                if (obj != null && !rowsPriceDiscounts.isNullOrEmpty()) {
                    newsAdd.forEach {
                        val rowsId = it.rowsBean.id
                        var price: String?
                        var inHandPrice: Double
                        rowsPriceDiscounts.filter { it?.skuId == rowsId }.let {
                            price = it.takeIf { it.isNotEmpty() }?.get(0)?.price
                            inHandPrice = it.takeIf { it.isNotEmpty() }?.get(0)?.inHandPrice?: 0.0
                        }
                        goodListAll.forEach { it ->
                            it.takeIf { rowsId == it.rowsBean.id }.let {
                                if ((it?.rowsBean?.fob ?: 0.0) >= inHandPrice) {
                                    it?.rowsBean?.showPriceAfterDiscount = price
                                }
                            }
                        }
                    }
                    goodsAdapter.notifyDataSetChanged()
                }
            }
        })
    }


    /**
     *  更新商品列表的数据
     *  0. 更新资质状态
     *  1. 拼团倒计时状态更新
     *  2. 更改刷新状态
     *  3. 请求折后价
     */
    fun <T : RowsBean> updateRowsData(licenseStatus: Int, data: MutableList<T>, adapter: YBMBaseAdapter<T>, isFirstRefrsh: Boolean, isEnd: Boolean) {
        // 更新资质状态
        if (licenseStatus != 0) {
            AuditStatusSyncUtil.getInstance().updateLicenseStatus(licenseStatus)
        }
        data.let {
            // 更新拼团倒计时数据
            addLocalTimeForRows(data)
            // 更新加载更多状态
            notifyAndControlLoadmoreStatus(data, adapter, isFirstRefrsh, isEnd)
            // 请求折后价
            getAfterDiscountPrice(data, adapter)
        }
    }


    fun notifyAndControlLoadmoreStatus(data: MutableList<*>?, adapter: YBMBaseAdapter<*>, isFirstRefrsh: Boolean, isEnd: Boolean) {
        if (isFirstRefrsh) {
            adapter.setNewData(data)
        } else {
            data?.takeIf { it.size > 0 }?.let { adapter.addData(it) }
        }
        if (isEnd) {
            if (isFirstRefrsh) {
                adapter.setEnableLoadMore(false)
            }
            adapter.loadMoreEnd(false)
        } else {
            adapter.loadMoreComplete()
        }
    }

    /**
     * @param rows 需要处理的商品列表数据
     */
    fun <T : RowsBean> addLocalTimeForRows(rows: MutableList<T>?) {
        rows?.forEach {
            it.actPt?.responseLocalTime = System.currentTimeMillis()
            it.actSk?.responseLocalTime = System.currentTimeMillis()
            it.limitFullDiscountActInfo?.responseLocalTime = System.currentTimeMillis()
        }
    }

    /**
     * @param rows 需要处理的商品列表Tags数据
     */
    fun <T : RowsBean> changeOrderFreeShippingTags(rows: MutableList<T>?) {
        rows?.forEach {
            it.tags?.orderFreeShippingTags = it.tags.productTags;
        }
    }

    /**
     * @param rows 需要处理的商品列表数据
     */
    fun addLocalTimeForRowsWithShop(rows: MutableList<HomeFeedRows>?) {
        rows?.filter { it.type == 1 }?.forEach {
            it.productInfo?.actPt?.responseLocalTime = System.currentTimeMillis()
            it.productInfo?.actSk?.responseLocalTime = System.currentTimeMillis()
        }
    }

    fun <T : SearchRowsBean> addLocalTimeForGiftSelect(rows: MutableList<T>?) {
        rows?.forEach {
            it.productInfo?.actPt?.responseLocalTime = System.currentTimeMillis()
            it.productInfo?.actSk?.responseLocalTime = System.currentTimeMillis()
            it.productInfo?.limitFullDiscountActInfo?.responseLocalTime = System.currentTimeMillis()
        }
    }

    /**
     * 验证库存+生成订单号
     * [subPos] 组合购永远回传当前选中的副品下标；加价购永远为-1
     * [loadingFlag] 是否显示laoding
     * [preLoad] 是否为数据加载，而非加购
     */
    fun checkCombinedProductNum(purchaseInfo: GroupPurchaseInfo, itemBean: RowsBeanCombinedExt, subPos:Int = -1, adt:YBMBaseAdapter<*>?=null,parentPos:Int=-1,loadingFlag:Boolean,preLoad:Boolean,callback:((GroupPurchaseInfo)->Unit)?=null){
        // 预加载，禁止再次加载
        if(preLoad && purchaseInfo.requestFlag){
            return
        }
        if(loadingFlag){
            RoutersUtils.showProgress()
        }
        val params = RequestParams()
        params.put("merchantId", SpUtil.getMerchantid())
        params.put("amount", itemBean.newQty.toString())
        params.put("skuId", itemBean.id.toString())
        params.put("scenceType", "0")
        if(preLoad){
            params.put("tipsType","2")
        }else{
            params.put("tipsType","1")
        }
        HttpManager.getInstance().post(AppNetConfig.CHANGECARTFORPROMOTION, params, object : BaseResponse<CartDataBean?>() {
            override fun onSuccess(content: String?, obj: BaseBean<CartDataBean?>?, baseBean: CartDataBean?) {
                purchaseInfo.requestFlag = true
                if (obj?.isSuccess() == true) {
                    // 校验通过，获取营销数据
                    // 超出库存时，数量改为库存数
                    val total = baseBean?.qty?:0
                    if(itemBean.newQty > total){
                        itemBean.newQty = total
                        // 无库存了，已未选中请求后台
                        if(total == 0){
                            itemBean.selectStatus = 0
                        }
                    }
                    if(baseBean?.businessState == 200 || itemBean.newQty > 0){
                        purchaseInfo.errMsg = ""
                        if(subPos != -1){
                            getGroupPurchaseInfo(purchaseInfo,adt,false,parentPos,baseBean?.tipsType == 2,callback)
                        }else{
                            getAdditionalPurchaseInfo(purchaseInfo,adt,false,parentPos,baseBean?.tipsType == 2,callback)
                        }
                    }else{
                        purchaseInfo.errMsg = baseBean?.message?:obj.dialog?.msg
                        if(itemBean.selectStatus == 1){
                            // 数量变更请求失败，重置回去
                            itemBean.newQty = itemBean.qty
                            itemBean.selectStatus = if(itemBean.qty > 0) 1 else 0
                        }
                        Handler(Looper.getMainLooper()).post {
                            if(parentPos == -1){
                                adt?.notifyDataSetChanged()
                            }else{
                                adt?.notifyItemChanged(parentPos,purchaseInfo)
                            }
                        }
                        dismissProgress()
                    }
                }else{
                    itemBean.newQty = itemBean.qty
                    itemBean.selectStatus = if(itemBean.qty > 0) 1 else 0
                    dismissProgress()
                }
            }

            override fun onFailure(error: NetError?) {
                purchaseInfo.requestFlag = true
                super.onFailure(error)
                itemBean.newQty = itemBean.qty
                itemBean.selectStatus = if(itemBean.qty > 0) 1 else 0
                dismissProgress()
            }
        })
    }

    /**
     * 获取组合购完整信息
     * [bean]   组合购大搜数据
     * [parentPosition] 组合购在整个大搜列表的位置
     * [loadingFlag] 是否显示loading
     * [showDialog] 是否显示错误弹框
     */
    fun getGroupPurchaseInfo(bean: GroupPurchaseInfo, adt:YBMBaseAdapter<*>?=null, loadingFlag:Boolean=false, parentPos:Int,preLoad:Boolean = false, callback:((GroupPurchaseInfo)->Unit)?=null) {
        if(loadingFlag){
            RoutersUtils.showProgress()
        }
        val requestParams = RequestParams()
        val combinedList = arrayListOf<Map<String, Any>>()
        combinedList.add(mapOf(
            "skuId" to bean.mainProduct.id,
            "qty" to if(bean.mainProduct.newQty == 0) bean.mainProduct.actPt?.skuStartNum?:1 else bean.mainProduct.newQty,
            "selectStatus" to bean.mainProduct.selectStatus,
            "isMainProduct" to true
        ))
        combinedList.add(mapOf(
            "skuId" to bean.subProducts[0].id,
            "qty" to if(bean.subProducts[0].newQty == 0) bean.subProducts[0].actPt?.skuStartNum?:1 else bean.subProducts[0].newQty,
            "selectStatus" to  bean.subProducts[0].selectStatus,
            "isMainProduct" to false
        ))
        requestParams.put("combinedList", JsonUtils.toJson(combinedList))
        requestParams.url = AppNetConfig.SORTNET_COMBINEDINFO_QUERY
        HttpManager.getInstance().post(requestParams, object : BaseResponse<GroupPurchaseInfo?>() {
            override fun onSuccess(content: String?, obj: BaseBean<GroupPurchaseInfo?>?, data: GroupPurchaseInfo?) {
                bean.requestFlag = true
                dismissProgress()
                if (obj != null && data != null) {
                    bean.realPay = data.realPay
                    bean.combinedDiscountSub = data.combinedDiscountSub
                    data.combinedList?.forEach {product->
                        if(product.isMainProduct){
                            bean.mainProduct.setOrderData(product)
                        }else{
                            bean.subProducts.forEachIndexed { index, originPro ->
                                if(originPro.id == product.id){
                                    bean.subProducts[index].setOrderData(product)
                                }
                            }
                        }
                    }
                    bean.errMsg = data.errMsg
                    callback?.invoke(bean)
                    Handler(Looper.getMainLooper()).post {
                        // 加购请求不为空直接弹
                        if(!data.errMsg.isNullOrEmpty() && !preLoad){
                            if (YBMAppLike.getApp().currActivity != null && YBMAppLike.getApp().currActivity is BaseActivity) {
                                AlertDialogEx(YBMAppLike.getApp().currActivity)
                                    .setTitle("")
                                    .setMessage(data.errMsg)
                                    .setMessageGravity(Gravity.CENTER)
                                    .setCanceledOnTouchOutside(false)
                                    .setConfirmButton(
                                        "我知道了"
                                    ) { dialog: AlertDialogEx?, _: Int ->
                                        dialog?.dismiss()
                                    }.show()
                            }
                        }
                        if(parentPos == -1){
                            adt?.notifyDataSetChanged()
                        }else{
                            adt?.notifyItemChanged(parentPos,bean)
                        }
                    }
                }else{
                    resetCombinedData(bean)
                    Handler(Looper.getMainLooper()).post {
                        if(parentPos == -1){
                            adt?.notifyDataSetChanged()
                        }else{
                            adt?.notifyItemChanged(parentPos,bean)
                        }
                    }
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                resetCombinedData(bean)
                bean.requestFlag = true
                Handler(Looper.getMainLooper()).post {
                    if(parentPos == -1){
                        adt?.notifyDataSetChanged()
                    }else{
                        adt?.notifyItemChanged(parentPos,bean)
                    }
                }
                dismissProgress()
            }
        })
    }

    /**
     * 订单查询失败时，也重置本地修改数量与选中状态
     */
    private fun resetCombinedData(bean: GroupPurchaseInfo){
        if(bean.mainProduct != null){
            bean.mainProduct.newQty = bean.mainProduct.qty
            bean.mainProduct.selectStatus = if(bean.mainProduct.qty > 0) 1 else 0
        }
        bean.subProducts.forEach {
            it.newQty = it.qty
            it.selectStatus = if(it.qty > 0) 1 else 0
        }
    }

    /**
     * 获取加价购完整信息
     * [bean]   组合购大搜数据
     * [subPos] 当前展示的副品位置
     * [loadingFlag] 是否显示loading
     * [parentPos] 加价购在大搜列表的位置
     */
    fun getAdditionalPurchaseInfo(bean: GroupPurchaseInfo, adt:YBMBaseAdapter<*>?=null, loadingFlag:Boolean=false, parentPos:Int=-1, preLoad:Boolean=false,callback:((GroupPurchaseInfo)->Unit)?=null) {
        if(loadingFlag){
            RoutersUtils.showProgress()
        }
        val requestParams = RequestParams()
        val combinedList = arrayListOf<Map<String, Any>>()

        bean.subProducts.forEach {
            combinedList.add(mapOf(
                "skuId" to it.id.toString(),
                "qty" to if(it.newQty == 0) it.actPt?.skuStartNum?:1 else it.newQty,
                "selectStatus" to it.selectStatus,
                "isMainProduct" to false
            ))
        }
        requestParams.put("combinedList", JsonUtils.toJson(combinedList))
        requestParams.url = AppNetConfig.SORTNET_COMBINEDINFO_QUERY
        HttpManager.getInstance().post(requestParams, object : BaseResponse<GroupPurchaseInfo?>() {
            override fun onSuccess(content: String?, obj: BaseBean<GroupPurchaseInfo?>?, data: GroupPurchaseInfo?) {
                dismissProgress()
                if (obj != null && data != null) {
                    bean.realPay = data.realPay
                    bean.combinedDiscountSub = data.combinedDiscountSub
                    bean.tips = data.tips
                    data.combinedList?.forEach {product->
                        if(product.isMainProduct){
                            bean.mainProduct.setOrderData(product)
                        }else{
                            bean.subProducts.forEachIndexed { index, originPro ->
                                if(originPro.id == product.id){
                                    bean.subProducts[index].setOrderData(product)
                                    return@forEach
                                }
                            }
                        }
                    }
                    bean.errMsg = data.errMsg
                    callback?.invoke(bean)
                    Handler(Looper.getMainLooper()).post {
                        if(!data.errMsg.isNullOrEmpty() && !preLoad){
                            if (YBMAppLike.getApp().currActivity != null && YBMAppLike.getApp().currActivity is BaseActivity) {
                                AlertDialogEx(YBMAppLike.getApp().currActivity)
                                    .setTitle("")
                                    .setMessage(data.errMsg)
                                    .setMessageGravity(Gravity.CENTER)
                                    .setCanceledOnTouchOutside(false)
                                    .setConfirmButton(
                                        "我知道了"
                                    ) { dialog: AlertDialogEx?, _: Int ->
                                        dialog?.dismiss()
                                    }.show()
                            }
                        }
                        if(parentPos == -1){
                            adt?.notifyDataSetChanged()
                        }else{
                            adt?.notifyItemChanged(parentPos,bean)
                        }
                    }
                }else{
                    resetCombinedData(bean)
                }
            }

            override fun onFailure(error: NetError?) {
                super.onFailure(error)
                resetCombinedData(bean)
                dismissProgress()
            }
        })
    }

    /**
     * 生成组合购、加价购订单。
     */
    fun preSettle(context:Context,purchaseInfo: GroupPurchaseInfo ,subPos: Int = -1) {
        val bizProducts = if(subPos != -1){
            // 主品未选中，库存不足了
            if(0 == purchaseInfo.mainProduct.selectStatus){
                if(purchaseInfo.errMsg.isNullOrEmpty()){
                    ToastUtils.showShort("您还没有加购${purchaseInfo.mainProduct.showName}哦，请先加购再下单")
                }else{
                    ToastUtils.showShort(purchaseInfo.errMsg)
                }
                return
            }
            if(0 == purchaseInfo.subProducts[0].selectStatus){
                if(purchaseInfo.errMsg.isNullOrEmpty()) {
                    ToastUtils.showShort("您还没有加购${purchaseInfo.subProducts[0].showName}哦，请先加购再下单")
                }else{
                    ToastUtils.showShort(purchaseInfo.errMsg)
                }
                return
            }
             arrayListOf(
                mapOf(
                    "skuId" to purchaseInfo.mainProduct.id.toString(),
                    "quantity" to purchaseInfo.mainProduct.newQty.toString()
                ), mapOf(
                    "skuId" to purchaseInfo.subProducts[0].id.toString(),
                    "quantity" to purchaseInfo.subProducts[0].newQty.toString()
                )
            )
        }else{
            val bizProducts = arrayListOf<Map<String, String>>()
            purchaseInfo.subProducts.forEach {
                if (it.selectStatus == 1) {
                    val map = mapOf(
                        "skuId" to it.id.toString(),
                        "quantity" to it.newQty.toString()
                    )
                    bizProducts.add(map)
                }
            }
            bizProducts
        }
        if (bizProducts.isEmpty()) {
            ToastUtils.showShort("您还没有加购商品哦，请先加购心仪的商品再下单")
            return
        }
        //把商品id和商户id传递到服务器
        RoutersUtils.showProgress()
        //不要在初始化的时候取值，因切换账户，所以id要随用随取
        val params = RequestParams()
        params.put("merchantId", SpUtil.getMerchantid())
        params.put("bizProducts", JsonUtils.toJson(bizProducts))
        HttpManager.getInstance().post(AppNetConfig.ORDER_V1_PRESETTLE, params, object : BaseResponse<SettleBean?>() {
            override fun onSuccess(content: String?, obj: BaseBean<SettleBean?>?, settleBean: SettleBean?) {
                dismissProgress()
                if (true == obj?.isSuccess() && settleBean != null) {
                    val checkOutIntent = Intent(context, PaymentActivity::class.java)
                    checkOutIntent.putExtra("tranNo", settleBean.tranNo)
                    checkOutIntent.putExtra(IntentCanst.COMBINED_PRODUCTS,JsonUtils.toJson(bizProducts))

                    // 构建组合购的shopInfoSxpList参数用于顺手买推荐
                    val shopInfoSxpList = buildShopInfoSxpListFromGroupPurchase(purchaseInfo)
                    checkOutIntent.putExtra("shopInfoSxpList", JsonUtils.toJson(shopInfoSxpList))
                    context.startActivity(checkOutIntent)
                }
            }

            override fun onFailure(error: NetError?) {
                dismissProgress()
            }
        })
    }

    /**
     * 从组合购数据构建shopInfoSxpList参数
     * 返回shopInfoSxpList的JSON字符串
     */
    private fun buildShopInfoSxpListFromGroupPurchase(purchaseInfo: GroupPurchaseInfo): List<ShopInfoSxpList> {
        val shopInfoSxpLists = mutableListOf<ShopInfoSxpList>()
        val mainSkuIds = arrayListOf<String>()
        mainSkuIds.add(purchaseInfo.mainProduct.id.toString());
        shopInfoSxpLists.add(ShopInfoSxpList(purchaseInfo.mainProduct.shopCode, mainSkuIds));
        purchaseInfo.subProducts.forEach { subProduct ->
            val skuIds = arrayListOf<String>()
            skuIds.add(subProduct.id.toString())
            shopInfoSxpLists.add(ShopInfoSxpList(subProduct.shopCode, mainSkuIds));
        }
        return shopInfoSxpLists;
    }
}