package com.ybmmarket20.view

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.LinearLayout
import androidx.constraintlayout.widget.ConstraintLayout
import com.ybmmarket20.R
import com.ybmmarket20.bean.payment.PaymentConsumeRebateDetailBean
import com.ybmmarket20.common.util.ConvertUtils
import com.ybmmarket20.databinding.ViewPayResultCashbackBinding
import com.ybmmarket20.utils.RoutersUtils
import com.ybmmarket20.utils.SpanUtils
import com.ybmmarket20.utils.UiUtils


/**
 * 提单页底部展示支付方式
 */
class PaymentResultCashbackView @JvmOverloads constructor(val mContext: Context, attr: AttributeSet? = null, def: Int = 0) :
    ConstraintLayout(mContext, attr, def) {
    // 单view宽度
    var itemWidth = (ConvertUtils.getScreenWidth() - ConvertUtils.dp2px(32f)) / 5f
    val vBinding by lazy {
        ViewPayResultCashbackBinding.inflate(LayoutInflater.from(context), this@PaymentResultCashbackView, true)
    }

    var mTrackClickListener:(()->Unit)? = null
    var mData:PaymentConsumeRebateDetailBean? = null

    init {
        layoutParams = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
        vBinding.root.setOnClickListener {
            mTrackClickListener?.invoke()
            if (!mData?.appUrls.isNullOrEmpty()) {
                RoutersUtils.open("ybmpage://commonh5activity?url=${mData?.appUrls}")
            }
        }
    }

    fun setData(data: PaymentConsumeRebateDetailBean) {
        // item小于4，放大单个item大小，保证横行铺满
        if(data.actResultList!!.size < 4){
            itemWidth = (ConvertUtils.getScreenWidth() - ConvertUtils.dp2px(32f)) / (data.actResultList!!.size + 1f)
        }
        mData = data
        if ("2" == data.levelScene || "3" == data.levelScene) {
            // 未达到活动门槛
            vBinding.tvContent.visibility = VISIBLE
            vBinding.tvContent.text =
                SpanUtils().append("当前订单金额${data.money}元，仅差")
                    .append("${data.nextLevelShortAmountForActPage.toString()}元")
                    .setForegroundColor(UiUtils.getColor(R.color.color_red_FF0000))
                    .append("，可得")
                    .apply {
                        if((data.nextLevelRedPacketAmount?.toDouble()?:0.0) > 0.0){
                            append("${data.nextLevelRedPacketAmount.toString()}元")
                            setForegroundColor(UiUtils.getColor(R.color.color_red_FF0000))
                        }
                    }
                    .append("红包。")
                    .create()
        } else if ("4" == data.levelScene && data.realAmount?.toDouble()?:0.0 != data.allLevelMaxAmount?.toDouble()?:0.0) {
            vBinding.tvContent.visibility = VISIBLE
            // 最高门槛
            vBinding.tvContent.text = SpanUtils().append("当前订单金额${data.money}元，最高可得")
                .append("${data.maxReturnRedPackageAmount.toString()}元")
                .setForegroundColor(UiUtils.getColor(R.color.color_red_FF0000))
                .append("红包。")
                .create()
        } else {
            vBinding.tvContent.visibility = GONE
        }
        if (!data.actResultList.isNullOrEmpty()) {
            val dataWidth: Int = (itemWidth * ((data.actResultList?.size ?: 0) + 1)).toInt()
            vBinding.progressBg.post {
                vBinding.progressBg.layoutParams = vBinding.progressBg.layoutParams.apply {
                    width = dataWidth
                }
            }
            calProgress(data, dataWidth)
        }
        addItem(data)
    }

    /**
     * 计算当前进度条长度
     */
    fun calProgress(data: PaymentConsumeRebateDetailBean, dataWidth: Int) {
        // 当前累计领取红包
        val realAmount = data.realAmount?.toDouble() ?: 0.0
        // 当前在哪个门槛前。各门槛红包累计为起始数
        val index = run {
            var currentSum = 0.0
            data.actResultList!!.indexOfFirst {
                currentSum += it.maxAmount?.toDouble() ?: 0.0
                realAmount <= currentSum
            }.takeIf { it != -1 } ?: -1
        }
        vBinding.progress.post {
            vBinding.progress.layoutParams = vBinding.progress.layoutParams.apply {
                width = if (index == data.actResultList!!.size - 1) {
                    // 最后一个，直接满格. 超出最高了
                    dataWidth - ConvertUtils.dp2px(2f)
                } else {
                    var progressTotal = 0.0
                    if (index == 0) {
                        var currentProgress = realAmount
                        progressTotal = (data.actResultList!![0].maxAmount?.toDouble() ?: 1.0)
                        var rate = currentProgress / progressTotal
                        (itemWidth * 1.5 * rate).toInt()
                    } else {
                        val preAmount = data.actResultList!!.subList(0,index).sumOf { it.maxAmount?.toDouble()?:0.0 }
                        var currentProgress = realAmount - preAmount
                        progressTotal = (data.actResultList!![index].maxAmount?.toDouble() ?: 1.0)
                        var rate = currentProgress / progressTotal
                        (itemWidth * rate + (0.5 + index) * itemWidth).toInt()
                    }
                }
            }
        }
    }


    /**
     * 添加门槛
     */
    fun addItem(data: PaymentConsumeRebateDetailBean) {
        vBinding.lyProgressItem.removeAllViews()
        val lp = LinearLayout.LayoutParams(itemWidth.toInt(), LinearLayout.LayoutParams.WRAP_CONTENT)
        vBinding.lyProgressItem.addView(
            PaymentResultCashbackItemView(mContext).apply {
                layoutParams = lp
                setData(null, true)
            }
        )
        if (!data.actResultList.isNullOrEmpty()) {
            data.actResultList?.forEach {
                vBinding.lyProgressItem.addView(
                    PaymentResultCashbackItemView(mContext).apply {
                        layoutParams = lp
                        setData(it)
                    }
                )
            }
        }
    }
}